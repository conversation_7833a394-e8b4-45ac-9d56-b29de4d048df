#!/usr/bin/env python3
"""
Startup tasks for the application
"""
import os
import sys
import asyncio
import logging
from src.utils.poppler import install_poppler_if_needed
from src.services.papers import update_summaries
from src.services.entity_resolution import EntityResolutionService, run_periodic_entity_resolution
from src.utils.entity_dictionary import get_entity_dictionary_manager

# Set up logging
logger = logging.getLogger("startup_tasks")


async def safe_task_wrapper(task_func, task_name, *args, **kwargs):
    """Wrapper to safely run tasks with exception handling"""
    try:
        await task_func(*args, **kwargs)
    except Exception as e:
        print(f"Error in task '{task_name}': {e}")
        import traceback
        traceback.print_exc()

async def start_update_task(arxiv_url, collections, openai_client):
    """
    Start the update task for remote mode

    Args:
        arxiv_url: The arXiv API URL
        collections: Dictionary of MongoDB collections
        openai_client: OpenAI client instance
    """
    try:
        # Check for Poppler installation (needed for PDF processing)
        install_poppler_if_needed()

        # Start the update task for remote mode with proper exception handling
        asyncio.create_task(
            safe_task_wrapper(
                update_summaries,
                "update_summaries",
                arxiv_url,
                collections['summaries'],
                collections['full_texts'],
                collections['reviews'],
                collections['sota_results'],
                collections['knowledge_graphs'],
                collections['daily_stats'],
                collections['mermaid_diagrams'],
                collections['tables'],
                openai_client
            )
        )
        print("Update task started successfully")
    except Exception as e:
        print(f"Error starting update task: {e}")
        import traceback
        traceback.print_exc()


async def run_kg_extraction(knowledge_graphs_collection, summaries_collection):
    """
    Run the knowledge graph extraction process

    Args:
        knowledge_graphs_collection: MongoDB collection for knowledge graphs
        summaries_collection: MongoDB collection for summaries
    """
    try:
        logger.info("Running knowledge graph extraction...")
        # Check if we have papers that need KG extraction
        # Filter out documents that don't have abs_url field to avoid KeyError
        papers_with_kg_urls = list(doc.get('abs_url') for doc in knowledge_graphs_collection.find({"abs_url": {"$exists": True}}, {'abs_url': 1}))
        papers_without_kg = summaries_collection.count_documents({
            'abs_url': {'$nin': papers_with_kg_urls}
        })

        if papers_without_kg > 0:
            logger.info(f"Found {papers_without_kg} papers that need knowledge graph extraction")
            # Import extract_kg functionality
            from scripts.knowledge_graph.extraction.extract_kg import process_papers
            # Process up to 50 papers at a time
            await process_papers(False, 50, False)
        else:
            logger.info("No papers need knowledge graph extraction")
    except Exception as e:
        logger.error(f"Error during knowledge graph extraction: {e}")
        import traceback
        traceback.print_exc()


async def sync_kg_to_neo4j(knowledge_graphs_collection, neo4j_driver, local_mode=False):
    """
    Background task to sync MongoDB knowledge graphs to Neo4j

    Args:
        knowledge_graphs_collection: MongoDB collection for knowledge graphs
        neo4j_driver: Neo4j driver instance
        local_mode: Whether running in local mode
    """
    # Import Neo4j availability flag
    from src.db.neo4j import NEO4J_AVAILABLE

    # Check if Neo4j is available
    if not NEO4J_AVAILABLE:
        print("Neo4j is not available. Skipping knowledge graph synchronization.")
        return

    # Fast path for local mode - create a sample knowledge graph
    if local_mode:
        print("Creating sample knowledge graph for testing in local mode...")
        create_sample_knowledge_graph(neo4j_driver)
        return

    # Import the necessary functions from mongodb_to_neo4j.py
    from scripts.knowledge_graph.neo4j.mongodb_to_neo4j import retrieve_all_knowledge_graphs, import_to_neo4j

    # Run extraction once at startup
    # Make sure collections are available before proceeding
    if knowledge_graphs_collection is not None and hasattr(knowledge_graphs_collection, 'database'):
        summaries_collection = getattr(knowledge_graphs_collection.database, 'summaries', None)
        if summaries_collection is not None:
            await run_kg_extraction(knowledge_graphs_collection, summaries_collection)
        else:
            print("Summaries collection not available, skipping KG extraction")
    else:
        print("Knowledge graphs collection not available, skipping KG extraction")

    # Immediately sync to Neo4j after extraction
    try:
        print("Starting MongoDB to Neo4j knowledge graph synchronization...")

        # Use our existing neo4j_driver which should already be properly set up
        # (either a real connection or an in-memory fallback)
        driver = neo4j_driver

        # Check for Neo4j database health and existing data
        with driver.session() as session:
            try:
                # Check if Neo4j is working
                result = session.run("MATCH (n) RETURN count(n) AS count")
                record = result.single()
                if record and "count" in record:
                    node_count = record["count"]
                    print(f"Current Neo4j database has {node_count} nodes")
                else:
                    # In-memory implementation might return a different structure
                    print("Neo4j database appears to be empty or returned unexpected structure")
            except Exception as e:
                print(f"Error counting Neo4j nodes: {e}")
                print("Continuing with Neo4j operations anyway")

        # Retrieve knowledge graphs from MongoDB that don't already exist in Neo4j
        if knowledge_graphs_collection is not None:
            print(f"Knowledge graphs collection count in MongoDB: {knowledge_graphs_collection.count_documents({})}")
            print(f"Knowledge graphs with abs_url: {knowledge_graphs_collection.count_documents({'abs_url': {'$exists': True}})}")
        else:
            print("Knowledge graphs collection is not available, cannot proceed with synchronization")
            return

        # Filter to only retrieve documents with abs_url field
        kg_documents = retrieve_all_knowledge_graphs(
            knowledge_graphs_collection,
            skip_existing=True,  # Only get graphs not already in Neo4j
            neo4j_driver=driver
        )

        # Additional filter to ensure abs_url is present in all documents
        kg_documents = [kg for kg in kg_documents if 'abs_url' in kg]
        print(f"Valid knowledge graphs for Neo4j after filtering: {len(kg_documents)}")

        if kg_documents and len(kg_documents) > 0:
            print(f"Found {len(kg_documents)} new knowledge graphs to sync to Neo4j")
            # Import to Neo4j - use environment variables for connection
            import os
            neo4j_uri = os.environ.get("NEO4J_URL_PRIVATE", "bolt://shuttle.proxy.rlwy.net:43630")
            # Fix the URI format to use proper bolt/neo4j protocol prefix
            if not (neo4j_uri.startswith("bolt://") or neo4j_uri.startswith("neo4j://")):
                # Add proper bolt protocol if missing
                neo4j_uri = f"bolt://{neo4j_uri}"
            neo4j_user = os.environ.get("NEO4J_USERNAME", "neo4j")
            neo4j_password = os.environ.get("NEO4J_PASSWORD", "neo4j")
            import_to_neo4j(kg_documents, neo4j_uri, neo4j_user, neo4j_password)
            print("Neo4j synchronization completed successfully")
        else:
            print("No new knowledge graphs to sync - Neo4j is up to date")

    except Exception as e:
        print(f"Error during Neo4j synchronization: {e}")
        import traceback
        traceback.print_exc()


def create_sample_knowledge_graph(neo4j_driver):
    """
    Create a simple sample knowledge graph for local testing

    Args:
        neo4j_driver: Neo4j driver instance (in-memory in local mode)
    """
    # Import Neo4j availability flag
    from src.db.neo4j import NEO4J_AVAILABLE

    # Check if Neo4j is available
    if not NEO4J_AVAILABLE and not hasattr(neo4j_driver, 'get_graph_data'):
        print("Neo4j is not available. Skipping sample knowledge graph creation.")
        return

    try:
        # Check if the driver has the in-memory graph interface
        if hasattr(neo4j_driver, 'get_graph_data'):
            # Sample entities and relationships for AI research
            entities = [
                {"id": "1", "name": "Transformer", "type": "Model"},
                {"id": "2", "name": "Attention", "type": "Mechanism"},
                {"id": "3", "name": "BERT", "type": "Model"},
                {"id": "4", "name": "GPT", "type": "Model"},
                {"id": "5", "name": "LLM", "type": "Concept"},
                {"id": "6", "name": "NLP", "type": "Field"},
                {"id": "7", "name": "Self-Attention", "type": "Mechanism"},
                {"id": "8", "name": "Fine-tuning", "type": "Technique"},
                {"id": "9", "name": "Transfer Learning", "type": "Technique"},
                {"id": "10", "name": "Embeddings", "type": "Concept"},
                {"id": "11", "name": "Tokenization", "type": "Technique"},
                {"id": "12", "name": "Prompt Engineering", "type": "Technique"},
                {"id": "13", "name": "RAG", "type": "Technique"},
                {"id": "14", "name": "Vector Database", "type": "Tool"},
                {"id": "15", "name": "Knowledge Graph", "type": "Concept"},
                {"id": "16", "name": "Entity Resolution", "type": "Technique"},
                {"id": "17", "name": "Semantic Search", "type": "Application"}
            ]

            relationships = [
                {"from": "1", "to": "2", "type": "USES"},
                {"from": "3", "to": "1", "type": "BASED_ON"},
                {"from": "4", "to": "1", "type": "BASED_ON"},
                {"from": "4", "to": "5", "type": "IS_A"},
                {"from": "3", "to": "5", "type": "IS_A"},
                {"from": "5", "to": "6", "type": "USED_IN"},
                {"from": "2", "to": "7", "type": "INCLUDES"},
                {"from": "3", "to": "8", "type": "SUPPORTS"},
                {"from": "3", "to": "9", "type": "ENABLES"},
                {"from": "4", "to": "9", "type": "ENABLES"},
                {"from": "5", "to": "10", "type": "GENERATES"},
                {"from": "5", "to": "11", "type": "REQUIRES"},
                {"from": "5", "to": "12", "type": "REQUIRES"},
                {"from": "13", "to": "5", "type": "USES"},
                {"from": "13", "to": "14", "type": "USES"},
                {"from": "15", "to": "16", "type": "REQUIRES"},
                {"from": "15", "to": "17", "type": "ENABLES"}
            ]

            # Add all entities
            for entity in entities:
                neo4j_driver.add_node(
                    entity["id"],
                    {"name": entity["name"], "type": entity["type"]}
                )

            # Add all relationships
            for rel in relationships:
                neo4j_driver.add_relationship(
                    rel["from"],
                    rel["to"],
                    rel["type"]
                )

            print(f"Created sample knowledge graph with {len(entities)} nodes and {len(relationships)} links")
        else:
            # For real Neo4j, run cypher queries to create the sample graph
            try:
                with neo4j_driver.session() as session:
                    # Clear existing data
                    session.run("MATCH (n) DETACH DELETE n")

                    # Create sample entities
                    session.run("""
                        CREATE (t:Model {name: 'Transformer'})
                        CREATE (a:Mechanism {name: 'Attention'})
                        CREATE (b:Model {name: 'BERT'})
                        CREATE (g:Model {name: 'GPT'})
                        CREATE (l:Concept {name: 'LLM'})
                        CREATE (n:Field {name: 'NLP'})
                        CREATE (s:Mechanism {name: 'Self-Attention'})
                        CREATE (f:Technique {name: 'Fine-tuning'})
                        CREATE (tr:Technique {name: 'Transfer Learning'})
                        CREATE (e:Concept {name: 'Embeddings'})
                        CREATE (tk:Technique {name: 'Tokenization'})
                        CREATE (p:Technique {name: 'Prompt Engineering'})
                        CREATE (r:Technique {name: 'RAG'})
                        CREATE (v:Tool {name: 'Vector Database'})
                        CREATE (k:Concept {name: 'Knowledge Graph'})
                        CREATE (er:Technique {name: 'Entity Resolution'})
                        CREATE (ss:Application {name: 'Semantic Search'})

                        CREATE (t)-[:USES]->(a)
                        CREATE (b)-[:BASED_ON]->(t)
                        CREATE (g)-[:BASED_ON]->(t)
                        CREATE (g)-[:IS_A]->(l)
                        CREATE (b)-[:IS_A]->(l)
                        CREATE (l)-[:USED_IN]->(n)
                        CREATE (a)-[:INCLUDES]->(s)
                        CREATE (b)-[:SUPPORTS]->(f)
                        CREATE (b)-[:ENABLES]->(tr)
                        CREATE (g)-[:ENABLES]->(tr)
                        CREATE (l)-[:GENERATES]->(e)
                        CREATE (l)-[:REQUIRES]->(tk)
                        CREATE (l)-[:REQUIRES]->(p)
                        CREATE (r)-[:USES]->(l)
                        CREATE (r)-[:USES]->(v)
                        CREATE (k)-[:REQUIRES]->(er)
                        CREATE (k)-[:ENABLES]->(ss)
                    """)

                    print("Created sample knowledge graph with 17 nodes and 17 links")
            except Exception as e:
                print(f"Error executing Neo4j queries: {e}")
                print("Neo4j may be unavailable. Skipping sample knowledge graph creation.")

    except Exception as e:
        print(f"Error creating sample knowledge graph: {e}")
        import traceback
        traceback.print_exc()


async def check_and_fix_missing_entities(collections, max_to_fix=100):
    """
    Check for and fix summaries with missing entities instead of deleting them.

    Args:
        collections: Dictionary of MongoDB collections
        max_to_fix: Maximum number of documents to process at startup (to avoid long startup times)
    """
    try:
        logger.info("Checking for summaries with missing entities...")

        summaries_collection = collections.get('summaries')
        if summaries_collection is None:
            logger.warning("Summaries collection not available, skipping entity check")
            return

        # Find documents where entities field is missing or empty
        missing_entities_query = {
            "$or": [
                {"entities": {"$exists": False}},
                {"entities": {"$eq": []}}
            ]
        }

        # Count missing entities
        missing_count = summaries_collection.count_documents(missing_entities_query)
        logger.info(f"Found {missing_count} summaries with missing entities")

        if missing_count == 0:
            logger.info("No missing entities to fix")
            return

        # Create default entities function
        def create_default_entities(doc):
            """
            Create default entities from tags or title for a document.
            Returns entities in standardized dict format with 'value' and 'category' keys.
            """
            default_entities = []

            # First try to use tags if available
            if doc.get('tags') and isinstance(doc['tags'], list):
                for tag in doc['tags']:
                    if tag and isinstance(tag, str):
                        default_entities.append({"value": tag, "category": "Tags"})

            # If no tags, try to extract entities from title
            if not default_entities and doc.get('title'):
                title = doc['title']
                # Extract simplistic entities from title (just words longer than 5 chars)
                words = [w for w in title.split() if len(w) > 5 and w.isalpha()]
                for word in words[:3]:  # Limit to first 3 words
                    default_entities.append({"value": word, "category": "Title"})

            # If still no entities, add a placeholder
            if not default_entities:
                default_entities.append({"value": "Generic Entity", "category": "Applications & Use Cases"})

            return default_entities

        # Fix all documents with missing entities up to max_to_fix
        fixed_count = 0
        docs_to_fix = list(summaries_collection.find(missing_entities_query).limit(max_to_fix))

        for doc in docs_to_fix:
            try:
                # Create default entities
                default_entities = create_default_entities(doc)

                # Update the document
                result = summaries_collection.update_one(
                    {"_id": doc["_id"]},
                    {"$set": {"entities": default_entities}}
                )

                if result.modified_count > 0:
                    fixed_count += 1

            except Exception as e:
                logger.error(f"Error updating document {doc.get('abs_url', 'unknown')}: {e}")

        logger.info(f"Fixed {fixed_count} summaries with default entities at startup")

        # Log remaining summaries with issues
        if missing_count > max_to_fix:
            remaining = missing_count - fixed_count
            logger.info(f"There are still {remaining} summaries with missing entities. "
                       f"They will be processed on next startup or run 'python fix_missing_entities.py' manually.")

    except Exception as e:
        logger.error(f"Error fixing summaries with missing entities: {e}")
        import traceback
        traceback.print_exc()


async def standardize_entity_formats(collections, max_to_fix=100):
    """
    Standardize entity formats in summaries to use consistent dictionary format.

    Args:
        collections: Dictionary of MongoDB collections
        max_to_fix: Maximum number of documents to process at startup
    """
    try:
        logger.info("Standardizing entity formats...")

        summaries_collection = collections.get('summaries')
        if summaries_collection is None:
            logger.warning("Summaries collection not available, skipping entity format standardization")
            return

        # Define function to standardize entity format
        def standardize_entity(entity):
            """Convert entity to standardized dictionary format"""
            if isinstance(entity, dict) and "value" in entity:
                # Already in dictionary format, ensure it has a category
                return {
                    "value": entity["value"],
                    "category": entity.get("category", "Uncategorized")
                }
            elif isinstance(entity, (list, tuple)) and len(entity) >= 2:
                # List/tuple format [value, category]
                return {
                    "value": entity[0],
                    "category": entity[1]
                }
            elif isinstance(entity, str):
                # Simple string format - treat as value and use default category
                return {
                    "value": entity,
                    "category": "Uncategorized"
                }
            else:
                # Invalid format
                logger.warning(f"Invalid entity format: {type(entity)}")
                return None

        # Find documents with non-standard entity formats
        # We need to check for: string entities, list/tuple entities, and dicts without 'category'
        # This is a bit complex to do in a MongoDB query, so we'll process in batches

        # First, process entities that are strings or lists
        non_dict_query = {
            "entities": {"$exists": True, "$ne": []},
            "$or": [
                {"entities.0": {"$type": "string"}},  # String entities
                {"entities.0.0": {"$exists": True}},  # Array/tuple entities (first element has index 0)
                {"entities.0.value": {"$exists": True}, "entities.0.category": {"$exists": False}}  # Dict without category
            ]
        }

        # Count non-standard entities
        try:
            non_standard_count = summaries_collection.count_documents(non_dict_query)
            logger.info(f"Found {non_standard_count} summaries with non-standard entity formats")
        except Exception as e:
            logger.error(f"Error counting non-standard entities: {e}")
            # Fall back to a simpler query
            non_dict_query = {
                "entities": {"$exists": True, "$ne": []}
            }
            non_standard_count = summaries_collection.count_documents(non_dict_query)
            logger.info(f"Found {non_standard_count} total summaries with entities (may include standard formats)")

        if non_standard_count == 0:
            logger.info("No entity formats to standardize")
            return

        # Process documents in batches
        fixed_count = 0
        docs_to_fix = list(summaries_collection.find(non_dict_query).limit(max_to_fix))

        for doc in docs_to_fix:
            try:
                entities = doc.get("entities", [])
                needs_update = False
                standardized_entities = []

                # Check if entities need standardization
                for entity in entities:
                    if isinstance(entity, str) or (isinstance(entity, (list, tuple)) and len(entity) > 0):
                        needs_update = True
                        break
                    elif isinstance(entity, dict) and "value" in entity and "category" not in entity:
                        needs_update = True
                        break

                if needs_update:
                    # Standardize all entities
                    for entity in entities:
                        std_entity = standardize_entity(entity)
                        if std_entity:
                            standardized_entities.append(std_entity)

                    # Update the document if we have valid entities to save
                    if standardized_entities:
                        result = summaries_collection.update_one(
                            {"_id": doc["_id"]},
                            {"$set": {"entities": standardized_entities}}
                        )

                        if result.modified_count > 0:
                            fixed_count += 1

                            # Log progress periodically
                            if fixed_count % 10 == 0:
                                logger.info(f"Standardized entity formats for {fixed_count} summaries so far")

            except Exception as e:
                logger.error(f"Error standardizing entities for document {doc.get('abs_url', 'unknown')}: {e}")

        logger.info(f"Standardized entity formats for {fixed_count} summaries at startup")

        # Log remaining summaries with issues
        if non_standard_count > max_to_fix and fixed_count < non_standard_count:
            remaining = non_standard_count - fixed_count
            logger.info(f"There are still approximately {remaining} summaries with non-standard entity formats. "
                       f"They will be processed on next startup.")

    except Exception as e:
        logger.error(f"Error standardizing entity formats: {e}")
        import traceback
        traceback.print_exc()


def add_synthetic_embeddings(collections, embedding_dim=1536, local_mode=False):
    """
    Add synthetic embeddings to papers in the summaries collection.
    This is needed for local testing of recommendation system.

    Args:
        collections: Dictionary with MongoDB collections
        embedding_dim: Dimension of embeddings to generate
        local_mode: Whether the app is running in local mode
    """
    if not local_mode:
        return  # Only run in local mode

    print("Adding synthetic embeddings to papers for local recommendation testing...")

    import numpy as np

    # Get all papers without embeddings
    papers_to_update = list(collections['summaries'].find({'embedding': {'$exists': False}}))
    update_count = 0

    # Bulk update with synthetic embeddings
    for paper in papers_to_update:
        # Generate a random embedding vector with the specified dimension
        embedding = np.random.rand(embedding_dim).tolist()

        # Update the paper document with the embedding using abs_url
        if 'abs_url' in paper:
            collections['summaries'].update_one(
                {'abs_url': paper['abs_url']},
                {'$set': {'embedding': embedding}}
            )
            update_count += 1

    print(f"Added synthetic embeddings to {update_count} papers")


async def start_entity_resolution(collections, local_mode=False):
    """
    Start entity resolution process at startup

    Args:
        collections: Dictionary of MongoDB collections
        local_mode: Whether running in local mode
    """
    try:
        logger.info("Starting entity resolution process at startup...")

        # Fast path for local mode with minimal operations
        if local_mode:
            # Initialize the entity dictionary manager with preset entities
            entity_dict_manager = get_entity_dictionary_manager(local_mode=True)

            # If we're running in local mode, add some sample entities to the collections
            summaries_collection = collections.get('summaries')
            if summaries_collection and summaries_collection.count_documents({}) == 0:
                # Add sample papers with entities if the collection is empty
                sample_papers = [
                    {
                        "title": "Attention Is All You Need",
                        "abs_url": "https://arxiv.org/abs/1706.03762",
                        "published": "2017-06-12",
                        "summary": "This paper introduces the Transformer architecture based on self-attention mechanisms.",
                        "tags": ["deep learning", "nlp"],
                        "entities": [
                            {"value": "Transformer", "category": "Architecture"},
                            {"value": "Attention", "category": "Mechanism"},
                            {"value": "Deep Learning", "category": "Field"},
                            {"value": "NLP", "category": "Field"}
                        ]
                    },
                    {
                        "title": "BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding",
                        "abs_url": "https://arxiv.org/abs/1810.04805",
                        "published": "2018-10-11",
                        "summary": "This paper introduces BERT, a pre-trained language model based on the Transformer architecture.",
                        "tags": ["nlp", "language model"],
                        "entities": [
                            {"value": "BERT", "category": "Model"},
                            {"value": "Transformer", "category": "Architecture"},
                            {"value": "NLP", "category": "Field"},
                            {"value": "Pre-training", "category": "Technique"}
                        ]
                    },
                    {
                        "title": "Retrieval-Augmented Generation for Knowledge-Intensive NLP Tasks",
                        "abs_url": "https://arxiv.org/abs/2005.11401",
                        "published": "2020-05-22",
                        "summary": "This paper introduces RAG, a retrieval-augmented generation approach for knowledge-intensive tasks.",
                        "tags": ["nlp", "retrieval", "generation"],
                        "entities": [
                            {"value": "RAG", "category": "Technique"},
                            {"value": "NLP", "category": "Field"},
                            {"value": "Retrieval", "category": "Technique"},
                            {"value": "Knowledge Graph", "category": "Concept"}
                        ]
                    }
                ]

                for paper in sample_papers:
                    try:
                        summaries_collection.insert_one(paper)
                    except Exception as e:
                        logger.error(f"Error inserting sample paper: {e}")

                logger.info(f"Added {len(sample_papers)} sample papers with entities for local testing")

            logger.info("Using preset entity dictionary in local mode for faster startup")
            return

        # Normal workflow for non-local mode
        # First check and fix missing entities
        await check_and_fix_missing_entities(collections)

        # Then standardize entity formats for existing entities
        await standardize_entity_formats(collections)

        # Create a service instance with our collections
        entity_service = EntityResolutionService(collections)

        # Initialize the entity dictionary manager
        entity_dict_manager = get_entity_dictionary_manager()
        logger.info(f"Loaded entity dictionary with {len(entity_dict_manager.entity_dict)} entries")

        # Update the entity dictionary from MongoDB
        entity_mappings = collections.get('entity_mappings')
        entity_resolution = collections.get('entity_resolution')
        if entity_mappings is not None and entity_resolution is not None:
            update_count = entity_dict_manager.update_from_mongodb(
                entity_mappings,
                entity_resolution
            )
            logger.info(f"Updated entity dictionary with {update_count} new mappings")

        # Check if we need to perform entity resolution
        # Get last update time
        last_update = None
        entity_resolution_coll = collections.get('entity_resolution')
        if entity_resolution_coll is not None:
            metadata_doc = entity_resolution_coll.find_one({"document_type": "metadata"})
            if metadata_doc:
                last_update = metadata_doc.get("last_update")

        # Get KG collection info
        kg_count = 0
        kg_collection = collections.get('knowledge_graphs')
        if kg_collection is not None:
            kg_count = kg_collection.count_documents({})

        # Get entity resolution collection info
        entity_count = 0
        if entity_resolution_coll is not None:
            entity_count = entity_resolution_coll.count_documents({"document_type": "entity"})

        logger.info(f"Found {kg_count} knowledge graphs and {entity_count} resolved entities")

        # Run entity resolution only if we have knowledge graphs but no entities,
        # or if KG count is significantly higher than entity count
        if kg_count > 0 and (entity_count == 0 or kg_count > entity_count * 1.2):
            logger.info("Entity resolution needed, starting process...")
            # Run entity resolution asynchronously with proper exception handling
            asyncio.create_task(
                safe_task_wrapper(
                    entity_service.resolve_entities,
                    "entity_resolution",
                    force_update=False
                )
            )
        else:
            logger.info("No entity resolution needed at startup")

        # Start the periodic entity resolution task regardless
        logger.info("Starting periodic entity resolution task...")
        asyncio.create_task(
            safe_task_wrapper(
                run_periodic_entity_resolution,
                "periodic_entity_resolution"
            )
        )

    except Exception as e:
        logger.error(f"Error during entity resolution startup: {e}")
        import traceback
        traceback.print_exc()